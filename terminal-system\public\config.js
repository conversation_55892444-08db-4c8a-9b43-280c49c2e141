// 配置管理JavaScript

// 获取当前服务器状态
const getCurrentConfig = async () => {
    try {
        const response = await fetch('/api/status');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('获取服务器状态失败:', error);
        return null;
    }
};

// 刷新访问地址列表
const refreshAddresses = async () => {
    const addressList = document.getElementById('addressList');
    const currentPort = document.getElementById('currentServerPort').textContent;
    
    // 清空现有列表
    addressList.innerHTML = '';
    
    // 添加本地地址
    const localAddresses = [
        `http://127.0.0.1:${currentPort}`,
        `http://localhost:${currentPort}`
    ];
    
    localAddresses.forEach(url => {
        addAddressItem('本地访问', url);
    });
    
    // 尝试获取局域网IP
    try {
        const response = await fetch('/api/network-info');
        const data = await response.json();
        
        if (data.localIPs && data.localIPs.length > 0) {
            data.localIPs.forEach(ip => {
                addAddressItem('局域网访问', `http://${ip}:${currentPort}`);
            });
        }
    } catch (error) {
        console.log('无法获取网络信息');
    }
};

// 添加地址项
const addAddressItem = (label, url) => {
    const addressList = document.getElementById('addressList');
    const item = document.createElement('div');
    item.className = 'address-item';
    item.innerHTML = `
        <div class="address-label">${label}</div>
        <div class="address-url">
            <a href="${url}" target="_blank">${url}</a>
            <button class="copy-btn" onclick="copyToClipboard('${url}')">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                </svg>
            </button>
        </div>
    `;
    addressList.appendChild(item);
};

// 复制到剪贴板
const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        showNotification('已复制到剪贴板', 'success');
    } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('已复制到剪贴板', 'success');
    }
};

// 显示通知
const showNotification = (message, type = 'info') => {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        background: var(--success-green);
        color: white;
        border-radius: 8px;
        box-shadow: var(--shadow-large);
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 2000);
};

// 应用预设配置
const applyPreset = (preset) => {
    const hostSelect = document.getElementById('hostSelect');
    const serverPortInput = document.getElementById('serverPortInput');
    const wsPortInput = document.getElementById('wsPortInput');
    
    switch (preset) {
        case 'local':
            hostSelect.value = '127.0.0.1';
            serverPortInput.value = '8080';
            wsPortInput.value = '8081';
            break;
        case 'lan':
            hostSelect.value = '0.0.0.0';
            serverPortInput.value = '8080';
            wsPortInput.value = '8081';
            break;
        case 'custom':
            hostSelect.value = '0.0.0.0';
            serverPortInput.value = '3000';
            wsPortInput.value = '3001';
            break;
    }
    
    showNotification(`已应用${preset === 'local' ? '本地开发' : preset === 'lan' ? '局域网访问' : '自定义'}配置`);
};

// 生成启动命令
const generateStartCommand = () => {
    const host = document.getElementById('hostSelect').value;
    const serverPort = document.getElementById('serverPortInput').value;
    const wsPort = document.getElementById('wsPortInput').value;
    
    const windowsCommand = `set HOST=${host} && set SERVER_PORT=${serverPort} && set WS_PORT=${wsPort} && npm start`;
    const linuxCommand = `HOST=${host} SERVER_PORT=${serverPort} WS_PORT=${wsPort} npm start`;
    
    document.getElementById('windowsCommand').textContent = windowsCommand;
    document.getElementById('linuxCommand').textContent = linuxCommand;
    
    const commandCard = document.getElementById('commandCard');
    commandCard.style.display = 'block';
    commandCard.scrollIntoView({ behavior: 'smooth' });
    
    showNotification('启动命令已生成', 'success');
};

// 复制命令
const copyCommand = (platform) => {
    const command = platform === 'windows'
        ? document.getElementById('windowsCommand').textContent
        : document.getElementById('linuxCommand').textContent;

    copyToClipboard(command);
};

// WebSocket连接管理
let testWebSocket = null;
let wsConnectionStatus = 'disconnected';

// 更新WebSocket连接状态
const updateWsConnectionStatus = (status, message = '') => {
    wsConnectionStatus = status;
    const statusElement = document.getElementById('wsConnectionStatus');
    const dot = statusElement.querySelector('.status-dot');
    const text = statusElement.querySelector('span');

    dot.className = `status-dot ${status}`;

    switch (status) {
        case 'connected':
            text.textContent = '已连接';
            break;
        case 'connecting':
            text.textContent = '连接中...';
            break;
        case 'disconnected':
            text.textContent = message || '未连接';
            break;
        case 'error':
            text.textContent = message || '连接错误';
            break;
    }
};

// 测试WebSocket连接
const testWsConnection = () => {
    const protocol = document.getElementById('wsProtocolSelect').value;
    const address = document.getElementById('wsAddressInput').value.trim();

    if (!address) {
        showNotification('请输入WebSocket地址', 'error');
        return;
    }

    const fullUrl = protocol + address;

    // 关闭现有连接
    if (testWebSocket) {
        testWebSocket.close();
        testWebSocket = null;
    }

    updateWsConnectionStatus('connecting');
    showNotification('正在测试连接...', 'info');

    try {
        testWebSocket = new WebSocket(fullUrl);

        testWebSocket.onopen = () => {
            updateWsConnectionStatus('connected');
            showNotification('连接测试成功！', 'success');

            // 发送测试消息
            if (testWebSocket.readyState === WebSocket.OPEN) {
                testWebSocket.send(JSON.stringify({
                    type: 'test',
                    message: 'Hello from config page!',
                    timestamp: Date.now()
                }));
            }
        };

        testWebSocket.onmessage = (event) => {
            console.log('收到测试消息:', event.data);
            showNotification('收到服务器响应', 'success');
        };

        testWebSocket.onclose = (event) => {
            updateWsConnectionStatus('disconnected', `连接关闭 (${event.code})`);
            testWebSocket = null;
        };

        testWebSocket.onerror = (error) => {
            updateWsConnectionStatus('error', '连接失败');
            showNotification('连接测试失败，请检查地址是否正确', 'error');
            testWebSocket = null;
        };

        // 连接超时处理
        setTimeout(() => {
            if (testWebSocket && testWebSocket.readyState === WebSocket.CONNECTING) {
                testWebSocket.close();
                updateWsConnectionStatus('error', '连接超时');
                showNotification('连接超时，请检查网络或服务器状态', 'error');
            }
        }, 10000);

    } catch (error) {
        updateWsConnectionStatus('error', '连接错误');
        showNotification(`连接错误: ${error.message}`, 'error');
    }
};

// 选择WebSocket服务器
const selectWsServer = (serverType) => {
    const protocolSelect = document.getElementById('wsProtocolSelect');
    const addressInput = document.getElementById('wsAddressInput');

    // 移除之前的选中状态
    document.querySelectorAll('.server-item').forEach(item => {
        item.classList.remove('selected');
    });

    // 添加选中状态
    event.target.closest('.server-item').classList.add('selected');

    switch (serverType) {
        case 'local':
            protocolSelect.value = 'ws://';
            addressInput.value = '127.0.0.1:8081';
            break;
        case 'echo':
            protocolSelect.value = 'wss://';
            addressInput.value = 'echo.websocket.org';
            break;
        case 'custom':
            protocolSelect.value = 'ws://';
            addressInput.value = '';
            addressInput.focus();
            break;
    }

    showNotification(`已选择${serverType === 'local' ? '本地服务器' : serverType === 'echo' ? 'Echo测试服务器' : '自定义服务器'}`, 'success');
};

// 保存WebSocket配置
const saveWsConfig = () => {
    const protocol = document.getElementById('wsProtocolSelect').value;
    const address = document.getElementById('wsAddressInput').value.trim();
    const autoReconnect = document.getElementById('autoReconnect').checked;
    const showPing = document.getElementById('showPing').checked;

    if (!address) {
        showNotification('请输入WebSocket地址', 'error');
        return;
    }

    const config = {
        protocol,
        address,
        fullUrl: protocol + address,
        autoReconnect,
        showPing,
        timestamp: Date.now()
    };

    // 保存到localStorage
    localStorage.setItem('wsConfig', JSON.stringify(config));

    // 同步到主页面的URL地址栏
    if (window.opener && !window.opener.closed) {
        try {
            window.opener.postMessage({
                type: 'updateWsConfig',
                config: config
            }, '*');
        } catch (error) {
            console.log('无法同步到主页面');
        }
    }

    showNotification('WebSocket配置已保存', 'success');
};

// 加载保存的WebSocket配置
const loadWsConfig = () => {
    try {
        const savedConfig = localStorage.getItem('wsConfig');
        if (savedConfig) {
            const config = JSON.parse(savedConfig);

            document.getElementById('wsProtocolSelect').value = config.protocol || 'ws://';
            document.getElementById('wsAddressInput').value = config.address || '';
            document.getElementById('autoReconnect').checked = config.autoReconnect !== false;
            document.getElementById('showPing').checked = config.showPing || false;

            console.log('已加载保存的WebSocket配置:', config);
        }
    } catch (error) {
        console.log('加载WebSocket配置失败:', error);
    }
};

// 页面加载完成
document.addEventListener('DOMContentLoaded', async () => {
    // 获取当前配置
    const config = await getCurrentConfig();
    if (config) {
        // 这里可以更新当前配置显示
        console.log('当前服务器配置:', config);
    }

    // 刷新地址列表
    refreshAddresses();

    // 加载WebSocket配置
    loadWsConfig();

    // 添加WebSocket相关事件监听器
    document.getElementById('testWsConnection').addEventListener('click', testWsConnection);
    document.getElementById('saveWsConfig').addEventListener('click', saveWsConfig);

    // 监听来自主页面的消息
    window.addEventListener('message', (event) => {
        if (event.data.type === 'requestWsConfig') {
            // 主页面请求WebSocket配置
            const savedConfig = localStorage.getItem('wsConfig');
            if (savedConfig) {
                event.source.postMessage({
                    type: 'wsConfigResponse',
                    config: JSON.parse(savedConfig)
                }, '*');
            }
        }
    });
    
    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        .config-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-6);
        }
        
        .config-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-8);
            padding: var(--space-6);
            background: var(--surface-primary);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(20px);
        }
        
        .config-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }
        
        .config-subtitle {
            color: var(--text-secondary);
            margin: var(--space-2) 0 0 0;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--space-6);
        }
        
        .config-card {
            background: var(--surface-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(20px);
            overflow: hidden;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-5);
            border-bottom: 1px solid var(--border-secondary);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .status-badge {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-badge.running {
            background: var(--success-green);
            color: white;
        }
        
        .card-content {
            padding: var(--space-5);
        }
        
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-3) 0;
            border-bottom: 1px solid var(--border-secondary);
        }
        
        .config-item:last-child {
            border-bottom: none;
        }
        
        .config-item label {
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .config-value {
            color: var(--text-primary);
            font-family: var(--font-mono);
            font-weight: 600;
        }
        
        .address-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-3) 0;
            border-bottom: 1px solid var(--border-secondary);
        }
        
        .address-item:last-child {
            border-bottom: none;
        }
        
        .address-label {
            color: var(--text-secondary);
            font-size: 13px;
            min-width: 80px;
        }
        
        .address-url {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }
        
        .address-url a {
            color: var(--primary-blue);
            text-decoration: none;
            font-family: var(--font-mono);
            font-size: 13px;
        }
        
        .copy-btn {
            width: 24px;
            height: 24px;
            border: none;
            background: var(--surface-secondary);
            border-radius: var(--radius-sm);
            color: var(--text-tertiary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-fast);
        }
        
        .copy-btn:hover {
            background: var(--surface-tertiary);
            color: var(--text-secondary);
        }
        
        .copy-btn svg {
            width: 12px;
            height: 12px;
        }
        
        .preset-configs {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }
        
        .preset-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-4);
            background: var(--surface-secondary);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all var(--transition-normal);
        }
        
        .preset-item:hover {
            background: var(--surface-tertiary);
            transform: translateY(-2px);
        }
        
        .preset-icon {
            font-size: 24px;
        }
        
        .preset-name {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .preset-desc {
            font-size: 13px;
            color: var(--text-secondary);
        }
        
        .config-form {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }
        
        .form-actions {
            margin-top: var(--space-2);
        }
        
        .command-section {
            margin-bottom: var(--space-4);
        }
        
        .command-label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--space-2);
        }
        
        .command-box {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3);
            background: var(--background-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-secondary);
        }
        
        .command-box code {
            flex: 1;
            font-family: var(--font-mono);
            font-size: 13px;
            color: var(--text-primary);
            word-break: break-all;
        }
        
        .command-note {
            margin-top: var(--space-4);
            padding: var(--space-3);
            background: var(--primary-blue-light);
            border-radius: var(--radius-md);
            border-left: 3px solid var(--primary-blue);
        }
        
        .command-note p {
            margin: 0;
            font-size: 13px;
            color: var(--text-secondary);
        }

        /* WebSocket连接配置样式 */
        .connection-status {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: 13px;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: var(--success-green);
        }

        .status-dot.disconnected {
            background: var(--text-quaternary);
            animation: none;
        }

        .status-dot.connecting {
            background: var(--warning-orange);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .url-input-group {
            display: flex;
            align-items: center;
            gap: 0;
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            overflow: hidden;
            background: var(--surface-secondary);
        }

        .url-input-group .protocol-select {
            border: none;
            background: var(--surface-tertiary);
            padding: var(--space-3) var(--space-2);
            border-right: 1px solid var(--border-secondary);
        }

        .url-input-group .form-input {
            border: none;
            background: transparent;
            margin: 0;
        }

        .flex-1 {
            flex: 1;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--space-3);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            cursor: pointer;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 16px;
            height: 16px;
            border: 2px solid var(--border-secondary);
            border-radius: var(--radius-sm);
            position: relative;
            transition: all var(--transition-fast);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '';
            position: absolute;
            left: 4px;
            top: 1px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .preset-servers {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .server-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-4);
            background: var(--surface-secondary);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all var(--transition-normal);
            border: 1px solid var(--border-secondary);
        }

        .server-item:hover {
            background: var(--surface-tertiary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .server-item.selected {
            border-color: var(--primary-blue);
            background: var(--primary-blue-light);
        }

        .server-icon {
            font-size: 20px;
            width: 32px;
            text-align: center;
        }

        .server-info {
            flex: 1;
        }

        .server-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-1);
        }

        .server-url {
            font-size: 12px;
            color: var(--text-tertiary);
            font-family: var(--font-mono);
        }

        .server-status {
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .local-status {
            background: var(--success-green);
            color: white;
        }

        .public-status {
            background: var(--primary-blue);
            color: white;
        }

        .custom-status {
            background: var(--warning-orange);
            color: white;
        }

        .websocket-form {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }
    `;
    document.head.appendChild(style);
});
