<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器配置 - 智能终端管理系统</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="bg-decoration">
        <div class="bg-gradient"></div>
        <div class="bg-mesh"></div>
    </div>
    
    <div class="config-container">
        <header class="config-header">
            <div class="header-content">
                <h1 class="config-title">服务器配置</h1>
                <p class="config-subtitle">自定义服务器监听地址和端口</p>
            </div>
            <div class="header-actions">
                <a href="/" class="btn btn-secondary">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                    </svg>
                    返回主页
                </a>
            </div>
        </header>
        
        <main class="config-main">
            <div class="config-grid">
                <!-- 当前配置 -->
                <div class="config-card">
                    <div class="card-header">
                        <h2 class="card-title">当前配置</h2>
                        <div class="status-badge running">运行中</div>
                    </div>
                    <div class="card-content">
                        <div class="config-item">
                            <label>监听地址</label>
                            <div class="config-value" id="currentHost">0.0.0.0</div>
                        </div>
                        <div class="config-item">
                            <label>HTTP端口</label>
                            <div class="config-value" id="currentServerPort">8080</div>
                        </div>
                        <div class="config-item">
                            <label>WebSocket端口</label>
                            <div class="config-value" id="currentWsPort">8081</div>
                        </div>
                    </div>
                </div>
                
                <!-- 访问地址 -->
                <div class="config-card">
                    <div class="card-header">
                        <h2 class="card-title">可用访问地址</h2>
                        <button class="btn btn-small" onclick="refreshAddresses()">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                            </svg>
                            刷新
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="address-list" id="addressList">
                            <div class="address-item">
                                <div class="address-label">本地访问</div>
                                <div class="address-url">
                                    <a href="http://127.0.0.1:8080" target="_blank">http://127.0.0.1:8080</a>
                                    <button class="copy-btn" onclick="copyToClipboard('http://127.0.0.1:8080')">
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速配置 -->
                <div class="config-card">
                    <div class="card-header">
                        <h2 class="card-title">快速配置</h2>
                    </div>
                    <div class="card-content">
                        <div class="preset-configs">
                            <div class="preset-item" onclick="applyPreset('local')">
                                <div class="preset-icon">🏠</div>
                                <div class="preset-info">
                                    <div class="preset-name">本地开发</div>
                                    <div class="preset-desc">127.0.0.1:8080</div>
                                </div>
                            </div>
                            <div class="preset-item" onclick="applyPreset('lan')">
                                <div class="preset-icon">🌐</div>
                                <div class="preset-info">
                                    <div class="preset-name">局域网访问</div>
                                    <div class="preset-desc">0.0.0.0:8080</div>
                                </div>
                            </div>
                            <div class="preset-item" onclick="applyPreset('custom')">
                                <div class="preset-icon">⚙️</div>
                                <div class="preset-info">
                                    <div class="preset-name">自定义配置</div>
                                    <div class="preset-desc">自定义端口</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- WebSocket连接配置 -->
                <div class="config-card">
                    <div class="card-header">
                        <h2 class="card-title">WebSocket连接配置</h2>
                        <div class="connection-status" id="wsConnectionStatus">
                            <div class="status-dot disconnected"></div>
                            <span>未连接</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="websocket-form">
                            <div class="form-group">
                                <label class="form-label">连接地址</label>
                                <div class="url-input-group">
                                    <select class="protocol-select" id="wsProtocolSelect">
                                        <option value="ws://">ws://</option>
                                        <option value="wss://">wss://</option>
                                    </select>
                                    <input type="text" class="form-input flex-1" id="wsAddressInput"
                                           placeholder="例如: echo.websocket.org" value="127.0.0.1:8081">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">连接选项</label>
                                <div class="options-grid">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="autoReconnect" checked>
                                        <span class="checkmark"></span>
                                        自动重连
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="showPing">
                                        <span class="checkmark"></span>
                                        显示心跳
                                    </label>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-primary" id="testWsConnection">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                    测试连接
                                </button>
                                <button type="button" class="btn btn-secondary" id="saveWsConfig">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7l-4-4zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm3-10H5V5h10v4z"/>
                                    </svg>
                                    保存配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 常用WebSocket服务器 -->
                <div class="config-card">
                    <div class="card-header">
                        <h2 class="card-title">常用WebSocket服务器</h2>
                    </div>
                    <div class="card-content">
                        <div class="preset-servers">
                            <div class="server-item" onclick="selectWsServer('local')">
                                <div class="server-icon">🏠</div>
                                <div class="server-info">
                                    <div class="server-name">本地服务器</div>
                                    <div class="server-url">ws://127.0.0.1:8081</div>
                                </div>
                                <div class="server-status local-status">本地</div>
                            </div>
                            <div class="server-item" onclick="selectWsServer('echo')">
                                <div class="server-icon">🔊</div>
                                <div class="server-info">
                                    <div class="server-name">Echo测试服务器</div>
                                    <div class="server-url">wss://echo.websocket.org</div>
                                </div>
                                <div class="server-status public-status">公共</div>
                            </div>
                            <div class="server-item" onclick="selectWsServer('custom')">
                                <div class="server-icon">⚙️</div>
                                <div class="server-info">
                                    <div class="server-name">自定义服务器</div>
                                    <div class="server-url">输入自定义地址</div>
                                </div>
                                <div class="server-status custom-status">自定义</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 服务器配置 -->
                <div class="config-card">
                    <div class="card-header">
                        <h2 class="card-title">服务器配置</h2>
                    </div>
                    <div class="card-content">
                        <form class="config-form" id="configForm">
                            <div class="form-group">
                                <label class="form-label">监听地址</label>
                                <select class="form-input" id="hostSelect">
                                    <option value="127.0.0.1">127.0.0.1 (仅本地)</option>
                                    <option value="0.0.0.0">0.0.0.0 (所有网络接口)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">HTTP端口</label>
                                <input type="number" class="form-input" id="serverPortInput" value="8080" min="1" max="65535">
                            </div>
                            <div class="form-group">
                                <label class="form-label">WebSocket端口</label>
                                <input type="number" class="form-input" id="wsPortInput" value="8081" min="1" max="65535">
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-primary" onclick="generateStartCommand()">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    生成启动命令
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- 启动命令 -->
                <div class="config-card" id="commandCard" style="display: none;">
                    <div class="card-header">
                        <h2 class="card-title">启动命令</h2>
                    </div>
                    <div class="card-content">
                        <div class="command-section">
                            <label class="command-label">Windows (CMD)</label>
                            <div class="command-box">
                                <code id="windowsCommand"></code>
                                <button class="copy-btn" onclick="copyCommand('windows')">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="command-section">
                            <label class="command-label">Linux/Mac (Bash)</label>
                            <div class="command-box">
                                <code id="linuxCommand"></code>
                                <button class="copy-btn" onclick="copyCommand('linux')">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="command-note">
                            <p>💡 复制命令后，在终端中运行即可使用自定义配置启动服务器</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="config.js"></script>
</body>
</html>
