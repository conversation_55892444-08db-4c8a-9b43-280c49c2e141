{"name": "智能终端管理系统服务器配置", "description": "可自定义服务器监听地址和端口", "configs": [{"name": "默认配置", "description": "本地开发环境", "host": "127.0.0.1", "serverPort": 8080, "wsPort": 8081, "env": {"HOST": "127.0.0.1", "SERVER_PORT": "8080", "WS_PORT": "8081"}}, {"name": "局域网配置", "description": "允许局域网访问", "host": "0.0.0.0", "serverPort": 8080, "wsPort": 8081, "env": {"HOST": "0.0.0.0", "SERVER_PORT": "8080", "WS_PORT": "8081"}}, {"name": "自定义端口", "description": "使用自定义端口", "host": "0.0.0.0", "serverPort": 3000, "wsPort": 3001, "env": {"HOST": "0.0.0.0", "SERVER_PORT": "3000", "WS_PORT": "3001"}}], "instructions": {"windows": ["使用环境变量启动:", "set HOST=0.0.0.0 && set SERVER_PORT=3000 && set WS_PORT=3001 && npm start", "", "或者修改 start-custom.bat 文件中的配置"], "linux": ["使用环境变量启动:", "HOST=0.0.0.0 SERVER_PORT=3000 WS_PORT=3001 npm start", "", "或者修改 start-custom.sh 文件中的配置"]}}