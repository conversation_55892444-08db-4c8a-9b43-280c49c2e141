#!/bin/bash

echo "========================================"
echo "智能终端管理系统 - 自定义配置启动"
echo "========================================"
echo

# 可自定义的配置项
# 修改下面的值来自定义服务器地址和端口

# 服务器监听地址 (0.0.0.0 = 所有网络接口, 127.0.0.1 = 仅本地)
export HOST=0.0.0.0

# HTTP服务器端口
export SERVER_PORT=8080

# WebSocket服务器端口
export WS_PORT=8081

echo "当前配置:"
echo "  监听地址: $HOST"
echo "  HTTP端口: $SERVER_PORT"
echo "  WebSocket端口: $WS_PORT"
echo
echo "启动服务器..."
echo

npm start

echo
echo "服务器已停止"
